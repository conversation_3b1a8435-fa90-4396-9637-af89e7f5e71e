name: End-to-End Tests

on:
  merge_group:
    branches: [dev, main]
  pull_request:
    branches: [dev, main, staging]
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      debug:
        description: 'Enable debug logging'
        required: false
        default: false
        type: boolean

env:
  # Docker configuration
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

  # Application configuration
  NODE_ENV: local
  TZ: UTC

  # Test configuration - use live mode for real HTTP calls to containerized app
  TEST_ENV: live
  ROSHI_URL: http://app-test:3000

jobs:
  test:
    name: End-to-End Tests in Docker
    runs-on: ubuntu-latest
    timeout-minutes: 30

    env:
      # Authentication and security
      API_AUTH_TOKEN: ${{ secrets.API_AUTH_TOKEN }}
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
      GH_AUTH_PACKAGE_TOKEN: ${{ secrets.GH_AUTH_PACKAGE_TOKEN }}

      # Firebase configuration
      FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
      FIREBASE_CLIENT_EMAIL: ${{ secrets.FIREBASE_CLIENT_EMAIL }}

      # Database configuration
      POSTGRES_HOST: database-test
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${{ secrets.POSTGRES_DB || 'postgres' }}
      POSTGRES_USER: ${{ secrets.POSTGRES_USER || 'postgres' }}
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD || 'password' }}

      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD || 'rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^' }}
      REDIS_TLS: false

      # Feature flags
      ENABLE_SIGNOZ: false
      CRON_ENABLED: false

    permissions:
      actions: read
      contents: read
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref || github.head_ref || github.ref_name }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            network=host

      - name: Create test results directory
        run: |
          mkdir -p test-results
          chmod 755 test-results

      - name: Enable debug logging
        if: ${{ github.event.inputs.debug == 'true' }}
        run: |
          echo "DOCKER_BUILDKIT_PROGRESS=plain" >> $GITHUB_ENV
          echo "BUILDX_NO_DEFAULT_ATTESTATIONS=1" >> $GITHUB_ENV

      - name: Clean up existing containers and volumes
        run: |
          echo "🧹 Cleaning up existing containers and volumes..."
          docker compose --profile test down -v --remove-orphans || true
          docker system prune -f || true

      - name: Build Docker images
        run: |
          echo "🏗️ Building Docker images..."
          docker compose --profile test build --parallel

          echo "📊 Docker images built:"
          docker images | grep roshi

      - name: Start infrastructure services
        run: |
          echo "🚀 Starting infrastructure services (PostgreSQL and Redis)..."
          docker compose --profile test up -d database-test redis

          echo "⏳ Waiting for services to be healthy..."
          timeout 120 bash -c '
            while ! docker compose exec database-test pg_isready -U $POSTGRES_USER -d $POSTGRES_DB; do
              echo "Waiting for PostgreSQL..."
              sleep 3
            done
          '

          timeout 60 bash -c '
            while ! docker compose exec redis redis-cli -a "$REDIS_PASSWORD" ping | grep -q PONG; do
              echo "Waiting for Redis..."
              sleep 2
            done
          '

          echo "✅ Infrastructure services are ready"

      - name: Start application service
        run: |
          echo "🚀 Starting application service..."
          docker compose --profile test up -d app-test

          echo "⏳ Waiting for application to be healthy..."
          timeout 180 bash -c '
            while ! docker compose exec app-test curl -f http://localhost:3000/health; do
              echo "Waiting for application... (checking health endpoint)"
              sleep 5
            done
          '

          echo "✅ Application service is ready"

      - name: Run database migrations
        run: |
          echo "🗃️ Running database migrations..."

          # Run migrations using the migrate service
          docker compose --profile test run --rm migrate

          echo "✅ Database migrations completed"

      - name: Verify service health
        run: |
          echo "🔍 Verifying all services are healthy..."

          echo "Database status:"
          docker compose exec database-test pg_isready -U $POSTGRES_USER -d $POSTGRES_DB

          echo "Redis status:"
          docker compose exec redis redis-cli -a "$REDIS_PASSWORD" ping

          echo "Application status:"
          docker compose exec app-test curl -f http://localhost:3000/health

          echo "✅ All services verified healthy"

      - name: Run end-to-end tests
        run: |
          echo "🧪 Running end-to-end tests..."

          # Run tests with proper error handling
          if docker compose --profile test run --rm test; then
            echo "✅ Tests completed successfully"
          else
            echo "❌ Tests failed"

            echo "📋 Container logs for debugging:"
            echo "=== Application logs ==="
            docker compose logs app-test --tail=50
            echo "=== Test runner logs ==="
            docker compose logs test --tail=50

            exit 1
          fi

      - name: Copy test results from container
        if: always()
        run: |
          echo "📋 Copying test results..."

          # Ensure test results exist and copy them
          if docker compose ps test | grep -q test; then
            docker compose cp test:/app/test-results/jest_results.json ./test-results/ || echo "No test results found in container"
          fi

          # List what we have
          echo "Test results directory contents:"
          ls -la test-results/ || echo "No test-results directory found"

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ github.run_id }}
          path: test-results/
          retention-days: 7
          if-no-files-found: warn

      - name: Display test summary
        if: always()
        run: |
          echo "📊 Test Summary"
          echo "=============="

          if [ -f "test-results/jest_results.json" ]; then
            echo "✅ Test results file found"

            # Extract basic stats from Jest results
            if command -v jq &> /dev/null; then
              echo "Test Statistics:"
              jq -r '.numTotalTests as $total | .numPassedTests as $passed | .numFailedTests as $failed | "Total: \($total), Passed: \($passed), Failed: \($failed)"' test-results/jest_results.json || echo "Could not parse test statistics"
            else
              echo "📄 Test results file exists but jq not available for parsing"
            fi
          else
            echo "⚠️ No test results file found"
          fi

      - name: Cleanup containers and volumes
        if: always()
        run: |
          echo "🧹 Cleaning up containers and volumes..."
          docker compose --profile test down -v --remove-orphans
          docker system prune -f

  comment:
    needs: test
    if: always()
    uses: ./.github/workflows/comment_test_result.yml
    secrets: inherit
    permissions:
      actions: read
      contents: read
      pull-requests: write
