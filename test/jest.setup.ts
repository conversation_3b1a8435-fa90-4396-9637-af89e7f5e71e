import 'reflect-metadata';
import config from './config';

// Mock @vendia/serverless-express
jest.mock('@vendia/serverless-express', () => ({
  configure: jest.fn().mockReturnValue((handler: any) => handler),
  eventContext: jest.fn().mockReturnValue({}),
}));

// Set test environment - preserve TEST_ENV if already set (for containerized tests)
if (!process.env.TEST_ENV) {
  process.env.NODE_ENV = 'test';
}

// Set required environment variables for testing (only if not already set)
process.env.JWT_SECRET = process.env.JWT_SECRET || config.jwtSecret;
process.env.JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || config.jwtRefreshSecret;
process.env.FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID || config.firebaseProjectId;
process.env.FIREBASE_PRIVATE_KEY = process.env.FIREBASE_PRIVATE_KEY || config.firebasePrivateKey;
process.env.FIREBASE_CLIENT_EMAIL = process.env.FIREBASE_CLIENT_EMAIL || config.firebaseClientEmail;
process.env.ROSHI_URL = process.env.ROSHI_URL || config.roshiUrl;
process.env.API_AUTH_TOKEN = process.env.API_AUTH_TOKEN || config.apiAuthToken;
process.env.SLACK_TOKEN = process.env.SLACK_TOKEN || config.slackToken;

// Log configuration for debugging in containerized environment
if (process.env.TEST_ENV === 'live') {
  console.log('🧪 Running in LIVE test mode');
  console.log('📡 ROSHI_URL:', process.env.ROSHI_URL);
  console.log('🔑 API_AUTH_TOKEN:', process.env.API_AUTH_TOKEN ? '***SET***' : 'NOT SET');
}

// Configure global Jest settings
jest.setTimeout(30000); // Set global timeout to 30 seconds

// Clear mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
});

// Clean up after all tests
afterAll(() => {
  jest.resetModules();
});
