# Docker Compose configuration for end-to-end testing
# Optimized for CI/CD with proper service orchestration and networking

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-test-data:
    driver: local
  redis-test-data:
    driver: local

services:
  # PostgreSQL database service for testing
  database-test:
    image: postgres:15-alpine
    container_name: roshi-postgres-test
    restart: unless-stopped
    networks:
      - test-network
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-postgres}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres-test-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    profiles:
      - test

  # Redis service with authentication for testing
  redis:
    image: redis:7-alpine
    container_name: roshi-redis-test
    restart: unless-stopped
    networks:
      - test-network
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}
    volumes:
      - redis-test-data:/data
    ports:
      - "6380:6379"
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    profiles:
      - test

  # Main application service for testing
  app-test:
    image: roshi-app-test:latest
    container_name: roshi-app-test
    build:
      context: .
      dockerfile: Dockerfile.test
      target: app
      args:
        GH_AUTH_PACKAGE_TOKEN: ${GH_AUTH_PACKAGE_TOKEN}
      cache_from:
        - roshi-app-test:latest
        - node:20-alpine
    restart: unless-stopped
    networks:
      - test-network
    ports:
      - '3001:3000'
    depends_on:
      database-test:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Application configuration
      NODE_ENV: local
      PORT: 3000
      TZ: UTC

      # Authentication and security
      API_AUTH_TOKEN: ${API_AUTH_TOKEN}
      JWT_SECRET: ${JWT_SECRET}
      SLACK_TOKEN: ${SLACK_TOKEN}

      # Firebase configuration
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}

      # Database configuration
      POSTGRES_HOST: database-test
      POSTGRES_PORT: 5432
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}

      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}
      REDIS_TLS: false

      # Feature flags and external services
      ENABLE_SIGNOZ: false
      CRON_ENABLED: false

      # Test environment specific
      TEST_ENV: ${TEST_ENV:-local}
      ROSHI_URL: ${ROSHI_URL:-http://localhost:3001}
    profiles:
      - test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Test runner service - executes the actual tests
  # Migration runner service - ensures database is properly set up
  migrate:
    image: roshi-app-test:latest
    container_name: roshi-migrate
    build:
      context: .
      dockerfile: Dockerfile.test
      target: app
      args:
        GH_AUTH_PACKAGE_TOKEN: ${GH_AUTH_PACKAGE_TOKEN}
    networks:
      - test-network
    depends_on:
      database-test:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Application configuration
      NODE_ENV: local
      TZ: UTC

      # Database configuration
      POSTGRES_HOST: database-test
      POSTGRES_PORT: 5432
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}

      # Redis configuration (needed for app startup)
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}
      REDIS_TLS: false

      # Minimal required environment for migrations
      JWT_SECRET: ${JWT_SECRET:-test-jwt-secret}
      API_AUTH_TOKEN: ${API_AUTH_TOKEN:-test-token}
    command: ["sh", "-c", "echo 'Running database migrations...' && npm run migration:run && echo 'Migrations completed successfully'"]
    profiles:
      - test

  # Test runner service - executes the actual tests
  test:
    image: roshi-test:latest
    container_name: roshi-test-runner
    build:
      context: .
      dockerfile: Dockerfile.test
      target: test
      args:
        GH_AUTH_PACKAGE_TOKEN: ${GH_AUTH_PACKAGE_TOKEN}
      cache_from:
        - roshi-test:latest
        - node:20-alpine
    networks:
      - test-network
    depends_on:
      app-test:
        condition: service_healthy
      database-test:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Test configuration
      NODE_ENV: test
      TZ: UTC

      # Authentication and security
      API_AUTH_TOKEN: ${API_AUTH_TOKEN}
      JWT_SECRET: ${JWT_SECRET}
      SLACK_TOKEN: ${SLACK_TOKEN}

      # Firebase configuration
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}

      # Database configuration
      POSTGRES_HOST: database-test
      POSTGRES_PORT: 5432
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}

      # Redis configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-rGH5Fq8!Au*Ja9Zpr_UTGYLxtdBYW^G^}
      REDIS_TLS: false

      # Test environment specific
      TEST_ENV: ${TEST_ENV:-local}
      ROSHI_URL: http://app-test:3000

      # Feature flags
      ENABLE_SIGNOZ: false
      CRON_ENABLED: false
    volumes:
      - ./test-results:/app/test-results
    profiles:
      - test
