# Optimized multi-stage Dockerfile for testing
# Designed for fast CI/CD execution with minimal layers and efficient caching

FROM node:20-alpine AS base

# Install essential system dependencies in a single layer
RUN apk --no-cache add \
    curl \
    tzdata \
    postgresql-client \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Set timezone
ENV TZ=UTC

# Add build argument for GitHub token
ARG GH_AUTH_PACKAGE_TOKEN

# Copy package files first for better Docker layer caching
COPY package*.json ./
COPY .npmrc ./

# Replace the token placeholder in .npmrc with the actual token
RUN sed -i "s/\${GH_AUTH_PACKAGE_TOKEN}/${GH_AUTH_PACKAGE_TOKEN}/g" .npmrc

# Install dependencies with optimized npm cache mount
RUN --mount=type=cache,target=/root/.npm \
    --mount=type=cache,target=/app/.npm \
    npm ci --prefer-offline --no-audit --silent

# Dependencies stage - separate for better caching
FROM base AS dependencies

# Copy source code and configuration files
COPY . .

# Build stage - optimized for speed
FROM dependencies AS builder

# Build the application with cache mount for faster builds
RUN --mount=type=cache,target=/app/.nest \
    --mount=type=cache,target=/app/node_modules/.cache \
    npm run build

# Application stage - for running the app with migrations
FROM base AS app

# Copy built application and necessary runtime files
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/tsconfig*.json ./

# Set environment variables for application
ENV NODE_ENV=local
ENV PORT=3000

# Expose port
EXPOSE 3000

# Health check for service readiness
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=5 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application with migrations
CMD ["sh", "-c", "echo 'Starting application...' && npm run migration:run && node dist/main.js"]

# Test stage - lightweight, optimized for test execution
FROM base AS test

# Copy only necessary files for testing to minimize image size
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/test ./test
COPY --from=builder /app/jest.config.js ./
COPY --from=builder /app/tsconfig*.json ./

# Set test environment variables
ENV NODE_ENV=test

# Create test-results directory with proper permissions
RUN mkdir -p /app/test-results && \
    chmod 755 /app/test-results

# Optimized test command with proper error handling and timeout
CMD ["sh", "-c", "\
    echo 'Starting test execution...' && \
    echo 'Environment: NODE_ENV='$NODE_ENV && \
    echo 'Waiting for application service to be ready...' && \
    timeout 180 sh -c 'until curl -f http://app-test:3000/health; do echo \"Waiting for app...\"; sleep 3; done' && \
    echo 'Application is ready, giving it a few more seconds to fully initialize...' && \
    sleep 10 && \
    echo 'Running tests...' && \
    npm run test -- --json --outputFile=/app/test-results/jest_results.json --verbose --detectOpenHandles --forceExit \
    "]