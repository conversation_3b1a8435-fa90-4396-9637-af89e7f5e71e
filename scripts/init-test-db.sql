-- PostgreSQL initialization script for testing environment
-- This script ensures the test database is properly configured

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create additional schemas if needed
-- CREATE SCHEMA IF NOT EXISTS test_schema;

-- Set up any test-specific configurations
-- ALTER DATABASE postgres SET timezone TO 'UTC';

-- Log initialization completion
DO $$
BEGIN
    RAISE NOTICE 'Test database initialization completed successfully';
END $$;
